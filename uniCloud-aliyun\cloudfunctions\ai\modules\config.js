/**
 * AI 配置和常量管理模块
 *
 * 主要功能：
 * 1. 管理豆包 AI 的连接配置参数
 * 2. 维护工具注册表，定义所有可用的云函数工具
 * 3. 定义 SSE 消息类型常量，统一前后端通信协议
 * 4. 提供默认系统提示词，指导 AI 进行意图识别
 *
 * 设计原则：
 * - 集中化配置管理，便于维护和修改
 * - 类型安全的参数定义，减少运行时错误
 * - 可扩展的工具注册机制，支持动态添加新工具
 * - 标准化的消息类型定义，确保通信协议一致性
 *
 * 使用场景：
 * - 初始化 AI 客户端时获取连接参数
 * - 执行计划生成时查询可用工具
 * - SSE 推送时使用标准消息类型
 * - 意图识别时使用默认系统提示词
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

/**
 * 豆包 AI 配置参数
 * 用于初始化 OpenAI 客户端，连接豆包 AI 服务
 *
 * 配置说明：
 * - baseURL: 豆包 AI 的 API 基础地址，使用北京区域的服务
 * - apiKey: API 访问密钥，生产环境应从环境变量或配置文件获取
 * - timeout: 请求超时时间，设置为 30 秒以平衡响应速度和稳定性
 *
 * 安全注意事项：
 * - API 密钥不应硬编码在代码中
 * - 建议使用环境变量或加密配置文件存储敏感信息
 * - 定期轮换 API 密钥以提高安全性
 */
const doubaoParams = {
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3', // 豆包 AI API 基础地址
  apiKey: 'a8b8b8b8-b8b8-b8b8-b8b8-b8b8b8b8b8b8', // API 密钥（生产环境需替换为真实密钥）
  timeout: 30000, // 请求超时时间（毫秒），30 秒超时
}

/**
 * 工具注册表
 * 定义所有可用的云函数工具及其配置信息
 *
 * 数据结构说明：
 * - cloudFunction: 对应的云函数名称，用于 uniCloud.importObject() 调用
 * - method: 云函数中的具体方法名
 * - description: 工具功能描述，用于 AI 理解工具用途
 * - parameters: 参数定义对象，包含类型、必需性、描述等信息
 * - metadata: 元数据信息，包含执行时间估算、工具分类等
 *
 * 工具分类：
 * - query: 查询类工具，用于获取数据
 * - action: 操作类工具，用于创建、修改、删除数据
 *
 * 扩展方式：
 * 添加新工具时，需要在此注册表中添加相应配置
 * 确保参数定义准确，以便参数验证器正确工作
 */
const TOOL_REGISTRY = {
  /**
   * 获取项目列表工具
   * 用于查询用户的所有项目或根据关键词筛选项目
   *
   * 使用场景：
   * - 用户询问"我有哪些项目"
   * - 需要根据项目名称查找特定项目
   * - 作为其他操作的前置步骤，获取项目上下文
   */
  getProjects: {
    cloudFunction: 'dida-todo', // 对应的云函数名称
    method: 'getProjects', // 调用的方法名
    description: '获取项目列表', // 工具功能描述
    parameters: {
      filter: {
        type: 'string', // 参数类型：字符串
        required: false, // 非必需参数
        description: '项目名称过滤关键词', // 参数说明
      },
    },
    metadata: {
      estimatedTime: 1500, // 预估执行时间（毫秒）
      category: 'query', // 工具分类：查询类
    },
  },
  /**
   * 获取任务列表工具
   * 用于查询指定项目下的任务或全局任务列表
   *
   * 使用场景：
   * - 用户询问"我有哪些任务"
   * - 查看特定项目下的任务
   * - 筛选已完成或未完成的任务
   */
  getTasks: {
    cloudFunction: 'dida-todo',
    method: 'getTasks',
    description: '获取任务列表',
    parameters: {
      projectId: { type: 'string', required: false, description: '项目 ID，不提供则获取所有任务' },
      completed: { type: 'boolean', required: false, description: '是否获取已完成任务，不提供则获取所有状态' },
      limit: { type: 'number', required: false, description: '返回数量限制，用于分页或性能优化' },
    },
    metadata: {
      estimatedTime: 2000, // 任务查询可能涉及更多数据，预估时间稍长
      category: 'query',
    },
  },

  /**
   * 创建任务工具
   * 用于根据用户输入创建新的任务
   *
   * 使用场景：
   * - 用户说"帮我创建一个任务"
   * - 从用户描述中提取任务信息并创建
   * - 批量创建多个相关任务
   */
  createTask: {
    cloudFunction: 'dida-todo',
    method: 'createTask',
    description: '创建任务',
    parameters: {
      taskData: { type: 'object', required: true, description: '任务数据对象，包含标题、描述、截止时间等信息' },
    },
    metadata: {
      estimatedTime: 2500, // 创建操作涉及数据写入，预估时间较长
      category: 'action', // 操作类工具
    },
  },

  /**
   * 获取项目详情工具
   * 用于获取特定项目的详细信息
   *
   * 使用场景：
   * - 需要了解项目的具体信息
   * - 作为其他操作的前置步骤，获取项目详情
   * - 验证项目是否存在
   */
  getProject: {
    cloudFunction: 'dida-todo',
    method: 'getProject',
    description: '获取项目详情',
    parameters: {
      projectId: { type: 'string', required: true, description: '项目 ID，必需参数' },
    },
    metadata: {
      estimatedTime: 1500,
      category: 'query',
    },
  },
}

/**
 * SSE 消息类型常量
 * 定义所有 SSE 推送消息的类型标识，确保前后端通信协议的一致性
 *
 * 消息类型分类：
 * 1. 基础流程消息：处理用户请求的基本流程
 * 2. 意图识别消息：AI 意图解析相关的消息
 * 3. 任务执行消息：智能任务执行过程中的消息
 *
 * 使用方式：
 * - 后端推送消息时使用这些类型标识
 * - 前端根据消息类型进行不同的处理逻辑
 * - 便于消息类型的统一管理和维护
 *
 * 扩展原则：
 * - 新增消息类型时应遵循命名规范
 * - 保持向后兼容性，避免修改已有类型
 * - 添加详细的中文描述，便于理解
 */
const SSE_MESSAGE_TYPES = {
  // 基础流程消息类型
  start: '开始生成回复', // 开始处理用户请求，通知前端显示加载状态
  intent_type: '意图类型识别', // 识别到用户意图类型，让前端知道请求的性质
  intent_content_start: '意图内容开始', // 开始推送意图内容，准备接收具体内容
  intent_content_chunk: '意图内容块', // 意图内容数据块，流式推送的内容片段
  end: '结束', // 处理完成，通知前端停止等待
  error: '错误', // 处理出错，通知前端显示错误信息

  // 任务执行相关消息类型（V1.1 新增）
  execution_plan_start: '执行计划开始', // 开始执行任务计划，显示计划信息
  execution_step: '执行步骤', // 当前执行步骤信息，显示进度
  step_result: '步骤结果', // 步骤执行结果，显示每步的执行情况
  step_error: '步骤错误', // 步骤执行出错，显示具体错误信息
  execution_complete: '执行完成', // 任务执行完成，显示最终结果
  execution_failed: '执行失败', // 任务执行失败，显示失败原因
}

/**
 * 默认系统提示词
 * 用于指导 AI 进行意图识别和内容提取的核心提示词
 *
 * 设计目标：
 * 1. 准确识别用户的真实意图
 * 2. 标准化 AI 的输出格式
 * 3. 确保后续处理的可靠性
 *
 * 意图类型定义（简化为两种）：
 * - task: 任务操作意图，包括创建、查询、修改、删除等所有任务相关操作
 * - chat: 普通聊天意图，不涉及具体的任务操作
 *
 * 输出格式要求：
 * - 使用中文引号「」标识关键字段
 * - 严格按照两行格式输出
 * - 不允许添加额外的解释或内容
 *
 * 关键词匹配规则：
 * - 任务类：创建、添加、设置、安排、新建、制定、查询、搜索、查看、显示、列出、找到、修改、更新、删除、完成、统计等
 * - 聊天类：问候、询问、讨论、解释等非任务操作
 */
const DEFAULT_SYSTEM_PROMPT = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

请分析用户输入内容，并将其归类为以下两种意图之一：
1. task: 当用户想要执行任何与任务相关的操作时（包括创建、查询、修改、删除任务等）
2. chat: 其他所有不属于任务操作的内容，视为一般闲聊对话

分析完成后，必须严格按照以下格式输出结果：
「意图类型」：[意图类型代码，必须是 task 或 chat 之一]
「意图内容」：[如果是任务操作，提取出具体的任务需求；如果是闲聊，则回复用户问题]

注意：
- 分析要准确，不要混淆不同意图类型
- 只输出上述指定的两行内容，不要添加任何其他解释或内容
- 确保格式严格遵循示例，包括使用中文引号「」
- task 类型包括：创建任务、查询任务、修改任务、删除任务、任务统计等所有任务相关操作
- chat 类型包括：问候、闲聊、询问非任务相关问题、讨论、解释等`

module.exports = {
  doubaoParams,
  TOOL_REGISTRY,
  SSE_MESSAGE_TYPES,
  DEFAULT_SYSTEM_PROMPT,
}
